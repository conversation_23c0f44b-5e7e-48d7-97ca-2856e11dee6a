package io.gigsta.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable
data class ResumeHistoryItem(
    @SerialName("id")
    val id: String,
    @SerialName("structured_data")
    val structuredData: JsonElement? = null,
    @SerialName("html")
    val htmlContent: String? = null,
    @SerialName("template_id")
    val templateId: String,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("tokens_deducted")
    val tokensDeducted: Boolean = false,
    @SerialName("status")
    val status: String? = null
)

@Serializable
data class ResumeTemplate(
    val id: String,
    val name: String
)
