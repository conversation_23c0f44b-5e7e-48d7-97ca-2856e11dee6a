package io.gigsta.data.repository

import io.gigsta.data.datasource.EmailDataSource
import io.gigsta.data.datasource.LetterDataSource
import io.gigsta.data.datasource.ResumeDataSource
import io.gigsta.domain.model.HistoryItem
import io.gigsta.domain.model.HistoryItemType
import io.gigsta.domain.repository.AuthRepository
import io.gigsta.domain.repository.HistoryRepository
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

class HistoryRepositoryImpl(
    private val authRepository: AuthRepository,
    private val resumeDataSource: ResumeDataSource = ResumeDataSource(),
    private val letterDataSource: LetterDataSource = LetterDataSource(),
    private val emailDataSource: EmailDataSource = EmailDataSource()
) : HistoryRepository {

    private suspend fun getCurrentUserId(): String? {
        return authRepository.getCurrentUser()?.id
    }
    
    override suspend fun getHistoryItems(type: HistoryItemType): List<HistoryItem> {
        val userId = getCurrentUserId() ?: return emptyList()

        return when (type) {
            HistoryItemType.CV_BUILDER -> {
                resumeDataSource.getResumeHistory(userId)
                    .getOrElse {
                        println("Failed to fetch resume history: ${it.message}")
                        emptyList()
                    }
                    .map { resume ->
                        HistoryItem(
                            id = resume.id,
                            title = extractTitleFromStructuredData(resume.structuredData) ?: "Resume",
                            subtitle = getResumeTemplateName(resume.templateId),
                            progress = if (resume.status == "done") 1.0f else 0.5f,
                            date = formatDate(resume.createdAt),
                            type = HistoryItemType.CV_BUILDER,
                            templateName = getResumeTemplateName(resume.templateId),
                            content = resume.structuredData?.toString(),
                            htmlContent = resume.htmlContent,
                            status = resume.status
                        )
                    }
            }
            HistoryItemType.APPLICATION_LETTER -> {
                letterDataSource.getLetterHistory(userId)
                    .getOrElse {
                        println("Failed to fetch letter history: ${it.message}")
                        emptyList()
                    }
                    .map { letter ->
                        HistoryItem(
                            id = letter.id,
                            title = extractTitleFromPlainText(letter.plainText) ?: "Application Letter",
                            subtitle = getLetterTemplateName(letter.templateId),
                            progress = if (letter.status == "done") 1.0f else 0.5f,
                            date = formatDate(letter.createdAt),
                            type = HistoryItemType.APPLICATION_LETTER,
                            templateName = getLetterTemplateName(letter.templateId),
                            content = letter.plainText,
                            htmlContent = letter.designHtml,
                            status = letter.status
                        )
                    }
            }
            HistoryItemType.EMAIL_APPLICATION -> {
                emailDataSource.getEmailHistory(userId)
                    .getOrElse {
                        println("Failed to fetch email history: ${it.message}")
                        emptyList()
                    }
                    .map { email ->
                        HistoryItem(
                            id = email.id,
                            title = email.subject,
                            subtitle = extractPreviewFromBody(email.body),
                            progress = 1.0f, // Emails are always complete
                            date = formatDate(email.createdAt),
                            type = HistoryItemType.EMAIL_APPLICATION,
                            content = email.body
                        )
                    }
            }
            HistoryItemType.JOB_MATCH -> {
                // TODO: Implement job match data source when available
                emptyList()
            }
        }
    }
    
    override suspend fun createHistoryItem(item: HistoryItem): Result<HistoryItem> {
        // TODO: Implement actual creation logic
        return Result.success(item)
    }

    override suspend fun updateHistoryItem(item: HistoryItem): Result<HistoryItem> {
        // TODO: Implement actual update logic
        return Result.success(item)
    }

    override suspend fun deleteHistoryItem(id: String): Result<Unit> {
        // TODO: Implement actual deletion logic
        return Result.success(Unit)
    }

    // Helper methods
    private fun formatDate(dateString: String): String {
        return try {
            val instant = Instant.parse(dateString)
            val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
            "${localDateTime.dayOfMonth} ${getMonthName(localDateTime.monthNumber)} ${localDateTime.year}"
        } catch (e: Exception) {
            dateString
        }
    }

    private fun getMonthName(month: Int): String {
        return when (month) {
            1 -> "Jan"
            2 -> "Feb"
            3 -> "Mar"
            4 -> "Apr"
            5 -> "May"
            6 -> "Jun"
            7 -> "Jul"
            8 -> "Aug"
            9 -> "Sep"
            10 -> "Oct"
            11 -> "Nov"
            12 -> "Dec"
            else -> "Unknown"
        }
    }

    private fun extractTitleFromStructuredData(structuredData: JsonElement?): String? {
        return try {
            structuredData?.jsonObject?.let { jsonObj ->
                // Try to extract a meaningful title from the structured data
                // Look for common fields like target_position, name, or title
                jsonObj["target_position"]?.jsonPrimitive?.content
                    ?: jsonObj["personal_info"]?.jsonObject?.get("name")?.jsonPrimitive?.content
                    ?: jsonObj["personalInfo"]?.jsonObject?.get("name")?.jsonPrimitive?.content
                    ?: jsonObj["name"]?.jsonPrimitive?.content
                    ?: jsonObj["title"]?.jsonPrimitive?.content
                    ?: jsonObj["position"]?.jsonPrimitive?.content
                    ?: "Resume"
            }
        } catch (e: Exception) {
            println("Error extracting title from structured data: ${e.message}")
            "Resume"
        }
    }

    private fun extractTitleFromPlainText(plainText: String): String? {
        // Extract first line or meaningful title from plain text
        return plainText.lines().firstOrNull()?.take(50) ?: "Application Letter"
    }

    private fun extractPreviewFromBody(body: String): String {
        // Extract first few words as preview
        return body.take(100).replace("\n", " ").trim()
    }

    private fun getResumeTemplateName(templateId: String): String {
        // TODO: Implement template name lookup
        return "Professional Template"
    }

    private fun getLetterTemplateName(templateId: String): String {
        // TODO: Implement template name lookup
        return "Standard Template"
    }
}
